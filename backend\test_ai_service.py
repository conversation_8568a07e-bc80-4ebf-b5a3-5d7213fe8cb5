#!/usr/bin/env python
"""
Test script to verify AI service configuration
"""
import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).resolve().parent
sys.path.insert(0, str(backend_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def test_gemini_api():
    """Test Gemini API directly"""
    print("🔍 Testing Gemini API directly...")
    
    try:
        import google.generativeai as genai
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        api_key = os.getenv('GEMINI_API_KEY')
        
        if not api_key:
            print("❌ GEMINI_API_KEY not found in environment")
            return False
            
        print(f"✅ API Key found: {api_key[:10]}...")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Initialize model
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Test generation
        response = model.generate_content("Hello, this is a test. Please respond with 'AI service is working!'")
        
        if response and response.text:
            print(f"✅ Gemini API Response: {response.text}")
            return True
        else:
            print("❌ No response from Gemini API")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API Error: {e}")
        return False

def test_ai_service_config():
    """Test Django AI service configuration"""
    print("\n🔍 Testing Django AI service configuration...")

    try:
        from core.ai_config import get_gemini_config

        # Force a fresh config reload
        config = get_gemini_config()
        config.reload_config()  # Force reload to pick up new environment

        status = config.get_status()

        print(f"AI Service Status: {status}")

        if status.get('available'):
            print("✅ AI service is available")

            # Test content generation
            print("🔍 Testing actual content generation...")
            test_content = config.generate_content("Test message: respond with 'Django AI service working!'")
            if test_content:
                print(f"✅ Content Generation: {test_content}")
                return True
            else:
                print("❌ Content generation failed - this is expected with expired API key")
                return False
        else:
            print("❌ AI service is not available")
            print(f"❌ Error: {status.get('error', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"❌ Django AI Service Error: {e}")
        return False

def main():
    print("🤖 AI Service Configuration Test\n")
    
    # Test 1: Direct Gemini API
    gemini_works = test_gemini_api()
    
    # Test 2: Django AI service
    django_ai_works = test_ai_service_config()
    
    print("\n📊 Test Results:")
    print(f"Direct Gemini API: {'✅ Working' if gemini_works else '❌ Failed'}")
    print(f"Django AI Service: {'✅ Working' if django_ai_works else '❌ Failed'}")
    
    if gemini_works and django_ai_works:
        print("\n🎉 All AI services are working correctly!")
        return True
    else:
        print("\n⚠️ AI service issues detected. Check configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
