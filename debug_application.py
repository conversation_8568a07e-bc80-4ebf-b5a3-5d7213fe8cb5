#!/usr/bin/env python
"""
Comprehensive Application Bug Detection Script
"""
import requests
import json
import subprocess
import sys
import os
from pathlib import Path

def check_backend_server():
    """Check if backend server is actually running and responding"""
    print("🔍 Checking Backend Server...")
    
    try:
        # Check if Django server is running
        response = requests.get('http://localhost:8000/', timeout=5)
        print(f"✅ Backend server responding: {response.status_code}")
        
        # Check API root
        api_response = requests.get('http://localhost:8000/api/', timeout=5)
        print(f"✅ API root responding: {api_response.status_code}")
        
        # Check AI status
        ai_response = requests.get('http://localhost:8000/api/ai/status/', timeout=5)
        if ai_response.status_code == 200:
            ai_data = ai_response.json()
            print(f"✅ AI Status: {ai_data.get('status', {}).get('available', False)}")
        else:
            print(f"❌ AI Status failed: {ai_response.status_code}")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is NOT running!")
        return False
    except Exception as e:
        print(f"❌ Backend error: {e}")
        return False

def check_frontend_server():
    """Check if frontend server is running"""
    print("\n🔍 Checking Frontend Server...")
    
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        print(f"✅ Frontend server responding: {response.status_code}")
        
        # Check if it's actually serving React content
        if 'react' in response.text.lower() or 'vite' in response.text.lower():
            print("✅ React application detected")
        else:
            print("⚠️ Frontend might not be serving React properly")
            
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Frontend server is NOT running!")
        return False
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        return False

def check_database_connection():
    """Check database connection and migrations"""
    print("\n🔍 Checking Database...")
    
    try:
        # Change to backend directory
        backend_dir = Path(__file__).parent / 'backend'
        os.chdir(backend_dir)
        
        # Check migrations
        result = subprocess.run(['python', 'manage.py', 'showmigrations'], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Database migrations check passed")
            # Count unapplied migrations
            unapplied = result.stdout.count('[ ]')
            if unapplied > 0:
                print(f"⚠️ {unapplied} unapplied migrations found")
            else:
                print("✅ All migrations applied")
        else:
            print(f"❌ Migration check failed: {result.stderr}")
            
        # Test database connection
        db_result = subprocess.run(['python', 'manage.py', 'check', '--database', 'default'], 
                                 capture_output=True, text=True, timeout=30)
        
        if db_result.returncode == 0:
            print("✅ Database connection working")
        else:
            print(f"❌ Database connection failed: {db_result.stderr}")
            
    except Exception as e:
        print(f"❌ Database check error: {e}")

def test_authentication_flow():
    """Test authentication endpoints"""
    print("\n🔍 Testing Authentication...")
    
    # Test registration endpoint
    try:
        reg_data = {
            'username': 'testuser123',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        reg_response = requests.post('http://localhost:8000/api/auth/register/', 
                                   json=reg_data, timeout=10)
        
        if reg_response.status_code in [200, 201]:
            print("✅ Registration endpoint working")
        elif reg_response.status_code == 400:
            print("✅ Registration endpoint responding (validation errors expected)")
        else:
            print(f"❌ Registration failed: {reg_response.status_code}")
            print(f"Response: {reg_response.text}")
            
    except Exception as e:
        print(f"❌ Registration test error: {e}")
    
    # Test login endpoint
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        login_response = requests.post('http://localhost:8000/api/auth/login/', 
                                     json=login_data, timeout=10)
        
        if login_response.status_code in [200, 201]:
            print("✅ Login endpoint working")
            return login_response.json().get('access_token')
        elif login_response.status_code == 400:
            print("✅ Login endpoint responding (invalid credentials expected)")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            
    except Exception as e:
        print(f"❌ Login test error: {e}")
        
    return None

def test_ai_functionality(token=None):
    """Test AI functionality with authentication"""
    print("\n🔍 Testing AI Functionality...")
    
    headers = {}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    # Test AI status (should work without auth)
    try:
        status_response = requests.get('http://localhost:8000/api/ai/status/', timeout=10)
        if status_response.status_code == 200:
            status_data = status_response.json()
            if status_data.get('status', {}).get('available'):
                print("✅ AI service is available")
            else:
                print("❌ AI service is not available")
                print(f"Status: {status_data}")
        else:
            print(f"❌ AI status check failed: {status_response.status_code}")
            
    except Exception as e:
        print(f"❌ AI status error: {e}")
    
    # Test AI chat (requires auth)
    try:
        chat_data = {
            'message': 'Hello, this is a test',
            'language': 'en'
        }
        
        chat_response = requests.post('http://localhost:8000/api/ai/chat/', 
                                    json=chat_data, headers=headers, timeout=30)
        
        if chat_response.status_code == 200:
            print("✅ AI chat working")
        elif chat_response.status_code == 401:
            print("⚠️ AI chat requires authentication (expected)")
        else:
            print(f"❌ AI chat failed: {chat_response.status_code}")
            print(f"Response: {chat_response.text}")
            
    except Exception as e:
        print(f"❌ AI chat error: {e}")

def check_common_issues():
    """Check for common application issues"""
    print("\n🔍 Checking Common Issues...")
    
    # Check if ports are in use
    try:
        import socket
        
        # Check port 8000 (backend)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8000))
        if result == 0:
            print("✅ Port 8000 is in use (backend should be running)")
        else:
            print("❌ Port 8000 is not in use (backend not running)")
        sock.close()
        
        # Check port 3000 (frontend)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 3000))
        if result == 0:
            print("✅ Port 3000 is in use (frontend should be running)")
        else:
            print("❌ Port 3000 is not in use (frontend not running)")
        sock.close()
        
    except Exception as e:
        print(f"❌ Port check error: {e}")

def main():
    print("🐛 COMPREHENSIVE BUG DETECTION")
    print("=" * 50)
    
    # Check servers
    backend_ok = check_backend_server()
    frontend_ok = check_frontend_server()
    
    # Check database
    check_database_connection()
    
    # Check common issues
    check_common_issues()
    
    # Test authentication
    token = test_authentication_flow()
    
    # Test AI functionality
    test_ai_functionality(token)
    
    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print(f"Backend: {'✅ OK' if backend_ok else '❌ FAILED'}")
    print(f"Frontend: {'✅ OK' if frontend_ok else '❌ FAILED'}")
    
    if not backend_ok:
        print("\n🚨 CRITICAL: Backend server is not running!")
        print("   Run: cd backend && python manage.py runserver 8000")
    
    if not frontend_ok:
        print("\n🚨 CRITICAL: Frontend server is not running!")
        print("   Run: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
