import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, Plus, Check, X, Filter, Lightbulb, AlertCircle } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { DashboardLayout } from '../dashboard';
import { BusinessIdea } from '../../../services/incubatorApi';
import { useAppSelector } from '../../../store/hooks';
import { AdvancedFilter, BulkActions } from '../common';
import { FilterValue } from '../common/AdvancedFilter';
import { useTranslation } from 'react-i18next';
// import {
//   fetchBusinessIdeas,
//   createBusinessIdea,
//   updateBusinessIdea,
//   deleteBusinessIdea
// } from '../../../store/incubatorSlice'; // TODO: Re-enable when incubator slice is restored
import { AppDispatch } from '../../../store/store';
// import { debugIncubatorPage, addIncubatorDebugToWindow } from '../../../utils/incubatorDebug';

import { useLanguage } from "../../../hooks/useLanguage";
const BusinessIdeasManagement: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const dispatch = useDispatch<AppDispatch>();

  // Redux state
  const { user } = useAppSelector(state => state.auth);
  // Temporary fallback while incubator slice is disabled
  const incubatorState = useAppSelector(state => (state as any).incubator);
  const {
    businessIdeas = [],
    isLoading: loading = false,
    error = null
  } = incubatorState || {};

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIdea, setSelectedIdea] = useState<BusinessIdea | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [activeFilters, setActiveFilters] = useState<Record<string, FilterValue>>({});
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [showDebugInfo, setShowDebugInfo] = useState(false);

  // Form data for create/edit
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    problem_statement: '',
    solution_description: '',
    target_audience: '',
    market_opportunity: '',
    business_model: '',
    current_stage: 'concept',
    moderation_status: 'pending',
    moderation_comment: '',
  });

  // Filter options
  const ideaFilterOptions = [
    {
      id: 'current_stage',
      label: t('admin.incubator.stage'),
      options: [
        { value: 'concept', label: t('admin.incubator.stages.concept') },
        { value: 'validation', label: t('admin.incubator.stages.validation') },
        { value: 'development', label: t('admin.incubator.stages.development') },
        { value: 'scaling', label: t('admin.incubator.stages.scaling') },
        { value: 'established', label: t('admin.incubator.stages.established') },
      ],
    },
    {
      id: 'moderation_status',
      label: t('admin.status'),
      options: [
        { value: 'approved', label: t('admin.approved') },
        { value: 'pending', label: t('admin.pending') },
        { value: 'rejected', label: t('admin.rejected') },
      ],
    },
  ];

  // Fetch business ideas and initialize debug
  useEffect(() => {
    loadBusinessIdeas();

    // Initialize debug utilities in development
    // if (process.env.NODE_ENV === 'development') {
    //   addIncubatorDebugToWindow();

    //   // Run debug check
    //   debugIncubatorPage(t, user).then(info => {
    //     setDebugInfo(info);

    //     // Show debug info if there are issues
    //     if (!info.apiConnectivity.businessIdeasEndpoint ||
    //         info.translationKeys.missingKeys.length > 0 ||
    //         !info.authenticationStatus.hasAdminAccess) {
    //       setShowDebugInfo(true);
    //     }
    //   });
    // }
  }, [t, user]);

  const loadBusinessIdeas = async () => {
    try {
      // await dispatch(fetchBusinessIdeas()).unwrap(); // TODO: Re-enable when incubator slice is restored
      console.log("Business ideas fetching temporarily disabled during maintenance.");
    } catch (error) {
      console.error('Error fetching business ideas:', error);
      setFormError(t('admin.incubator.failedToLoadIdeas'));
    }
  };

  // Handle filter change
  const handleFilterChange = (filters: Record<string, FilterValue>) => {
    setActiveFilters(filters);
  };

  // Filter business ideas based on search term and active filters
  const filteredIdeas = businessIdeas.filter(idea => {
    // Search filter
    const matchesSearch =
      idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      idea.description.toLowerCase().includes(searchTerm.toLowerCase());

    // Apply active filters
    let matchesFilters = true;
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        if (key === 'current_stage' && value !== idea.current_stage) {
          matchesFilters = false;
        }
        if (key === 'moderation_status' && value !== idea.moderation_status) {
          matchesFilters = false;
        }
      }
    });

    return matchesSearch && matchesFilters;
  });

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Open create modal
  const openCreateModal = () => {
    setFormData({
      title: '',
      description: '',
      problem_statement: '',
      solution_description: '',
      target_audience: '',
      market_opportunity: '',
      business_model: '',
      current_stage: 'concept',
      moderation_status: 'pending',
      moderation_comment: '',
    });
    setFormError(null);
    setFormSuccess(null);
    setIsCreateModalOpen(true);
  };

  // Open edit modal
  const openEditModal = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setFormData({
      title: idea.title,
      description: idea.description,
      problem_statement: idea.problem_statement,
      solution_description: idea.solution_description,
      target_audience: idea.target_audience,
      market_opportunity: idea.market_opportunity || '',
      business_model: idea.business_model || '',
      current_stage: idea.current_stage,
      moderation_status: idea.moderation_status,
      moderation_comment: idea.moderation_comment || '',
    });
    setFormError(null);
    setFormSuccess(null);
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const openDeleteModal = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setIsDeleteModalOpen(true);
  };

  // Handle create business idea
  const handleCreateIdea = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setFormSubmitting(true);
    setFormError(null);

    try {
      // await dispatch(createBusinessIdea({
      //   ...formData,
      //   owner_id: user.id,
      // })).unwrap(); // TODO: Re-enable when incubator slice is restored
      console.log("Create business idea temporarily disabled during maintenance:", formData);
      setFormSuccess("Business idea creation temporarily disabled during maintenance.");

      // Close modal after success
      setTimeout(() => {
        setIsCreateModalOpen(false);
        setFormSuccess(null);
      }, 1500);
    } catch (error) {
      console.error('Error creating business idea:', error);
      setFormError(t("admin.failed.to.create", "Failed to create business idea"));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handle update business idea
  const handleUpdateIdea = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedIdea || !user) return;

    setFormSubmitting(true);
    setFormError(null);

    try {
      // await dispatch(updateBusinessIdea({
      //   id: selectedIdea.id,
      //   ideaData: {
      //     ...formData,
      //     owner_id: selectedIdea.owner_id || user.id, // Preserve original owner or use current user
      //   }
      // })).unwrap(); // TODO: Re-enable when incubator slice is restored
      console.log("Update business idea temporarily disabled during maintenance:", formData);
      setFormSuccess("Business idea update temporarily disabled during maintenance.");

      // Close modal after success
      setTimeout(() => {
        setIsEditModalOpen(false);
        setFormSuccess(null);
      }, 1500);
    } catch (error) {
      console.error('Error updating business idea:', error);
      setFormError(t("admin.failed.to.update", "Failed to update business idea"));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handle delete business idea
  const handleDeleteIdea = async () => {
    if (!selectedIdea) return;

    setFormSubmitting(true);

    try {
      await dispatch(deleteBusinessIdea(selectedIdea.id)).unwrap();

      setFormSuccess(t("admin.business.idea.deleted", "Business idea deleted successfully"));

      // Close modal after success
      setTimeout(() => {
        setIsDeleteModalOpen(false);
        setFormSuccess(null);
      }, 1500);
    } catch (error) {
      console.error('Error deleting business idea:', error);
      setFormError(t("admin.failed.to.delete", "Failed to delete business idea"));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedItems.length === 0) return;

    setFormSubmitting(true);

    try {
      if (action === 'approve') {
        // Approve selected ideas
        for (const id of selectedItems) {
          await businessIdeasAPI.moderateIdea(id, 'approved');
        }
        setFormSuccess(`${selectedItems.length} ideas approved successfully`);
      } else if (action === 'reject') {
        // Reject selected ideas
        for (const id of selectedItems) {
          await businessIdeasAPI.moderateIdea(id, 'rejected');
        }
        setFormSuccess(`${selectedItems.length} ideas rejected successfully`);
      } else if (action === 'delete') {
        // Delete selected ideas
        for (const id of selectedItems) {
          await businessIdeasAPI.deleteBusinessIdea(id);
        }
        setFormSuccess(`${selectedItems.length} ideas deleted successfully`);
      }

      // Refresh data and clear selection
      await loadBusinessIdeas();
      setSelectedItems([]);
    } catch (error) {
      console.error('Error performing bulk action:', error);
      setFormError(t("admin.failed.to.perform", "Failed to perform bulk action"));
    } finally {
      setFormSubmitting(false);
    }
  };

  // Get bulk actions
  const getBulkActions = () => [
    { label: t("admin.approve.selected.value", "Approve Selected"), value: 'approve', icon: Check },
    { label: t("admin.reject.selected.value", "Reject Selected"), value: 'reject', icon: X },
    { label: t("admin.delete.selected.value", "Delete Selected"), value: 'delete', icon: Trash2 },
  ];

  return (
    <DashboardLayout currentPage="incubator">
      <div className={`mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
        <div>
          <h1 className="text-2xl font-bold">{t('admin.incubator.title')}</h1>
          <div className="text-gray-400 mt-1">{t('admin.incubator.description')}</div>
        </div>

      </div>

      {/* Error/Success Messages */}
      {formError && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
          {formError}
        </div>
      )}

      {formSuccess && (
        <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
          {formSuccess}
        </div>
      )}



      {/* Debug Panel (Development Only) */}
      {process.env.NODE_ENV === 'development' && debugInfo && showDebugInfo && (
        <div className="mb-6 p-4 bg-yellow-900/20 border border-yellow-600 rounded-lg">
          <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <AlertCircle size={20} className={`text-yellow-500 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              <h3 className="text-lg font-semibold text-yellow-200">{t("admin.debug.information", "Debug Information")}</h3>
            </div>
            <button
              onClick={() => setShowDebugInfo(false)}
              className="text-yellow-400 hover:text-yellow-300"
            >
              ×
            </button>
          </div>

          <div className="space-y-3 text-sm">
            {/* API Connectivity */}
            <div>
              <span className="font-medium text-yellow-200">{t("admin.api.connectivity", "API Connectivity:")}</span>
              <span className={`ml-2 ${debugInfo.apiConnectivity.businessIdeasEndpoint ? 'text-green-400' : 'text-red-400'}`}>
                {debugInfo.apiConnectivity.businessIdeasEndpoint ? '✅ Connected' : '❌ Failed'}
              </span>
              {debugInfo.apiConnectivity.errorMessage && (
                <div className="text-red-300 mt-1">Error: {debugInfo.apiConnectivity.errorMessage}</div>
              )}
            </div>

            {/* Translation Keys */}
            <div>
              <span className="font-medium text-yellow-200">{t("admin.translation.keys", "Translation Keys:")}</span>
              <span className={`ml-2 ${debugInfo.translationKeys.missingKeys.length === 0 ? 'text-green-400' : 'text-red-400'}`}>
                {debugInfo.translationKeys.missingKeys.length === 0
                  ? '✅ All keys found'
                  : `❌ ${debugInfo.translationKeys.missingKeys.length} missing keys`}
              </span>
              {debugInfo.translationKeys.missingKeys.length > 0 && (
                <div className="text-red-300 mt-1">
                  Missing: {debugInfo.translationKeys.missingKeys.slice(0, 3).join(', ')}
                  {debugInfo.translationKeys.missingKeys.length > 3 && '...'}
                </div>
              )}
            </div>

            {/* Authentication */}
            <div>
              <span className="font-medium text-yellow-200">{t("admin.authentication", "Authentication:")}</span>
              <span className={`ml-2 ${debugInfo.authenticationStatus.hasAdminAccess ? 'text-green-400' : 'text-red-400'}`}>
                {debugInfo.authenticationStatus.hasAdminAccess ? '✅ Admin access' : '❌ No admin access'}
              </span>
            </div>

            {/* Data */}
            <div>
              <span className="font-medium text-yellow-200">{t("admin.data", "Data:")}</span>
              <span className={`ml-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`}>
                {debugInfo.dataIntegrity.businessIdeasCount} business ideas loaded
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className={`mb-6 flex flex-col md:flex-row gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`relative flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
          <input
            type="text"
            placeholder={t('admin.incubator.searchIdeas')}
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full px-4 py-2 pl-10 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          />
          <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
        </div>

        <AdvancedFilter
          filterOptions={ideaFilterOptions}
          onFilterChange={handleFilterChange}
          activeFilters={activeFilters}
        />
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="mb-4">
          <BulkActions
            selectedCount={selectedItems.length}
            actions={getBulkActions()}
            onActionSelect={handleBulkAction}
            isSubmitting={formSubmitting}
          />
        </div>
      )}

      {/* Business Ideas Table */}
      {loading ? (
        <div className={`flex justify-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : filteredIdeas.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-indigo-900/50 border-b border-indigo-800">
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>
                  <input
                    type="checkbox"
                    checked={selectedItems.length === filteredIdeas.length && filteredIdeas.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems(filteredIdeas.map(idea => idea.id));
                      } else {
                        setSelectedItems([]);
                      }
                    }}
                    className="rounded bg-indigo-950 border-indigo-700 text-purple-600 focus:ring-purple-500"
                  />
                </th>
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>{t('admin.title')}</th>
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>{t('admin.owner')}</th>
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>{t('admin.incubator.stage')}</th>
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>{t('admin.status')}</th>
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>{t('admin.created')}</th>
                <th className={`p-3  ${isRTL ? "text-right" : "text-left"}`}>{t('admin.actions')}</th>
              </tr>
            </thead>
            <tbody>
              {filteredIdeas.map(idea => (
                <tr key={idea.id} className="border-b border-indigo-800/50 hover:bg-indigo-900/30">
                  <td className="p-3">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(idea.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedItems([...selectedItems, idea.id]);
                        } else {
                          setSelectedItems(selectedItems.filter(id => id !== idea.id));
                        }
                      }}
                      className="rounded bg-indigo-950 border-indigo-700 text-purple-600 focus:ring-purple-500"
                    />
                  </td>
                  <td className="p-3 font-medium">{idea.title}</td>
                  <td className="p-3">{idea.owner.username}</td>
                  <td className="p-3">
                    <span className="px-2 py-1 rounded text-xs capitalize">
                      {idea.current_stage}
                    </span>
                  </td>
                  <td className="p-3">
                    <span className={`px-2 py-1 rounded text-xs ${
                      idea.moderation_status === 'approved'
                        ? 'bg-green-900/50 text-green-200'
                        : idea.moderation_status === 'rejected'
                        ? 'bg-red-900/50 text-red-200'
                        : 'bg-yellow-900/50 text-yellow-200'}
                    }`}>
                      {idea.moderation_status}
                    </span>
                  </td>
                  <td className="p-3 text-sm text-gray-400">
                    {new Date(idea.created_at).toLocaleDateString()}
                  </td>
                  <td className="p-3">
                    <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
                      <button
                        onClick={() => openEditModal(idea)}
                        className="p-1 text-blue-400 hover:text-blue-300"
                        title={t('admin.incubator.moderate')}
                      >
                        <Check size={16} />
                      </button>
                      <button
                        onClick={() => openDeleteModal(idea)}
                        className="p-1 text-red-400 hover:text-red-300"
                        title={t("common.delete", "Delete")}
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-12 bg-indigo-900/20 rounded-lg border border-indigo-800">
          <Lightbulb size={48} className="mx-auto text-gray-500 mb-4" />
          <h3 className="text-xl font-semibold mb-2">{t('admin.incubator.noIdeasFound')}</h3>
          <div className="text-gray-400 mb-4">
            {searchTerm || Object.keys(activeFilters).length > 0
              ? t('admin.incubator.adjustFilters')
              : t('admin.incubator.noIdeasYet')}
          </div>
        </div>
      )}



      {/* Edit Modal */}
      {isEditModalOpen && selectedIdea && (
        <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-indigo-900 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                <h3 className="text-xl font-bold">{t('admin.incubator.moderateIdea')}</h3>
                <button
                  onClick={() => setIsEditModalOpen(false)}
                  className="text-gray-400 hover:text-white"
                >
                  &times;
                </button>
              </div>

              {formError && (
                <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
                  {formError}
                </div>
              )}

              {formSuccess && (
                <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
                  {formSuccess}
                </div>
              )}

              {/* Business Idea Details (Read-only) */}
              <div className="space-y-4 mb-6">
                <div className="bg-indigo-950/30 p-4 rounded-lg border border-indigo-800">
                  <h4 className="text-lg font-semibold mb-3">{t('admin.incubator.ideaDetails')}</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium mb-1 text-gray-300">
                        {t('admin.incubator.form.title')}
                      </label>
                      <div className="text-white">{selectedIdea.title}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1 text-gray-300">
                        {t('admin.incubator.form.currentStage')}
                      </label>
                      <div className="text-white">{t(`admin.incubator.stages.${selectedIdea.current_stage}`)}</div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1 text-gray-300">
                      {t('admin.incubator.form.description')}
                    </label>
                    <div className="text-white">{selectedIdea.description}</div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1 text-gray-300">
                      {t('admin.incubator.form.problemStatement')}
                    </label>
                    <div className="text-white">{selectedIdea.problem_statement}</div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1 text-gray-300">
                      {t('admin.incubator.form.solutionDescription')}
                    </label>
                    <div className="text-white">{selectedIdea.solution_description}</div>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1 text-gray-300">
                      {t('admin.incubator.form.targetAudience')}
                    </label>
                    <div className="text-white">{selectedIdea.target_audience}</div>
                  </div>

                  {selectedIdea.market_opportunity && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-1 text-gray-300">
                        {t('admin.incubator.form.marketOpportunity')}
                      </label>
                      <div className="text-white">{selectedIdea.market_opportunity}</div>
                    </div>
                  )}

                  {selectedIdea.business_model && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-1 text-gray-300">
                        {t('admin.incubator.form.businessModel')}
                      </label>
                      <div className="text-white">{selectedIdea.business_model}</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Moderation Controls */}
              <form onSubmit={handleUpdateIdea} className="space-y-4">
                <div className="bg-indigo-950/30 p-4 rounded-lg border border-indigo-800">
                  <h4 className="text-lg font-semibold mb-3">{t('admin.incubator.moderationControls')}</h4>

                  <div>
                    <label htmlFor="edit-moderation_status" className="block text-sm font-medium mb-1">
                      {t('admin.incubator.form.moderationStatus')}
                    </label>
                    <select
                      id="edit-moderation_status"
                      name="moderation_status"
                      value={formData.moderation_status}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    >
                      <option value="pending">{t('admin.pending')}</option>
                      <option value="approved">{t('admin.approved')}</option>
                      <option value="rejected">{t('admin.rejected')}</option>
                    </select>
                  </div>

                  <div className="mt-4">
                    <label htmlFor="edit-moderation_comment" className="block text-sm font-medium mb-1">
                      {t('admin.incubator.form.moderationComment')}
                    </label>
                    <textarea
                      id="edit-moderation_comment"
                      name="moderation_comment"
                      value={formData.moderation_comment}
                      onChange={handleInputChange}
                      rows={3}
                      placeholder={t('admin.incubator.moderationCommentPlaceholder')}
                      className="w-full px-3 py-2 bg-indigo-950/50 border border-indigo-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                    />
                  </div>
                </div>

                <div className={`flex justify-end space-x-4 pt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <button
                    type="button"
                    onClick={() => setIsEditModalOpen(false)}
                    className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
                  >
                    {t('common.cancel')}
                  </button>
                  <button
                    type="submit"
                    disabled={formSubmitting}
                    className={`px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    {formSubmitting ? (
                      <>
                        <span className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></span>
                        {t('admin.incubator.form.moderating')}
                      </>
                    ) : (
                      t('admin.incubator.form.updateModeration')
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedIdea && (
        <div className={`fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="bg-indigo-900 rounded-lg w-full max-w-md">
            <div className="p-6">
              <h3 className="text-xl font-bold mb-4">{t('admin.incubator.confirmDelete')}</h3>
              <div className="mb-6">
                {t('admin.incubator.confirmDeleteMessage', { title: selectedIdea.title })}
              </div>

              {formError && (
                <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md text-red-200">
                  {formError}
                </div>
              )}

              {formSuccess && (
                <div className="mb-4 p-3 bg-green-900/50 border border-green-500 rounded-md text-green-200">
                  {formSuccess}
                </div>
              )}

              <div className={`flex justify-end space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                <button
                  type="button"
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-800 transition-colors"
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="button"
                  onClick={handleDeleteIdea}
                  disabled={formSubmitting}
                  className={`px-4 py-2 bg-red-600 rounded-lg hover:bg-red-700 transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {formSubmitting ? (
                    <>
                      <span className={`w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 ${isRTL ? "space-x-reverse" : ""}`}></span>
                      {t('admin.incubator.form.deleting')}
                    </>
                  ) : (
                    t('admin.delete')
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default BusinessIdeasManagement;
