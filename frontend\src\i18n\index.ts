import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Import English translations
import enCommon from '../locales/en/common.json';
import enAuth from '../locales/en/auth.json';
import enNavigation from '../locales/en/navigation.json';
import enAI from '../locales/en/ai.json';
import enAdmin from '../locales/en/admin.json';
import enMentorship from '../locales/en/mentorship.json';
import enTemplates from '../locales/en/templates.json';
import enHome from '../locales/en/home.json';
import enHero from '../locales/en/hero.json';
import enFeatures from '../locales/en/features.json';
import enAnalytics from '../locales/en/analytics.json';
import enDashboard from '../locales/en/dashboard.json';
import enIncubator from '../locales/en/incubator.json';
import enPosts from '../locales/en/posts.json';
import enEvents from '../locales/en/events.json';
import enResources from '../locales/en/resources.json';
import enProfile from '../locales/en/profile.json';
import enAbout from '../locales/en/about.json';
import enBusinessPlan from '../locales/en/businessPlan.json';
import enCrud from '../locales/en/crud.json';
import enFunding from '../locales/en/funding.json';
import enRoles from '../locales/en/roles.json';
import enBusinessPlans from '../locales/en/businessPlans.json';
import enInvestments from '../locales/en/investments.json';
import enMentees from '../locales/en/mentees.json';
import enModeration from '../locales/en/moderation.json';
import enPortfolio from '../locales/en/portfolio.json';
import enReports from '../locales/en/reports.json';
import enSuperAdmin from '../locales/en/superAdmin.json';
import enCommunity from '../locales/en/community.json';
import enFooter from '../locales/en/footer.json';
import enContact from '../locales/en/contact.json';
import enForum from '../locales/en/forum.json';

// Import Arabic translations
import arCommon from '../locales/ar/common.json';
import arAuth from '../locales/ar/auth.json';
import arNavigation from '../locales/ar/navigation.json';
import arAI from '../locales/ar/ai.json';
import arAdmin from '../locales/ar/admin.json';
import arMentorship from '../locales/ar/mentorship.json';
import arTemplates from '../locales/ar/templates.json';
import arHome from '../locales/ar/home.json';
import arHero from '../locales/ar/hero.json';
import arFeatures from '../locales/ar/features.json';
import arAnalytics from '../locales/ar/analytics.json';
import arDashboard from '../locales/ar/dashboard.json';
import arIncubator from '../locales/ar/incubator.json';
import arPosts from '../locales/ar/posts.json';
import arEvents from '../locales/ar/events.json';
import arResources from '../locales/ar/resources.json';
import arProfile from '../locales/ar/profile.json';
import arAbout from '../locales/ar/about.json';
import arBusinessPlan from '../locales/ar/businessPlan.json';
import arCrud from '../locales/ar/crud.json';
import arFunding from '../locales/ar/funding.json';
import arRoles from '../locales/ar/roles.json';
import arBusinessPlans from '../locales/ar/businessPlans.json';
import arInvestments from '../locales/ar/investments.json';
import arMentees from '../locales/ar/mentees.json';
import arModeration from '../locales/ar/moderation.json';
import arPortfolio from '../locales/ar/portfolio.json';
import arReports from '../locales/ar/reports.json';
import arSuperAdmin from '../locales/ar/superAdmin.json';
import arCommunity from '../locales/ar/community.json';
import arFooter from '../locales/ar/footer.json';
import arContact from '../locales/ar/contact.json';
import arForum from '../locales/ar/forum.json';

// Get initial language from localStorage or default to 'en'
const getInitialLanguage = (): string => {
  const savedLanguage = localStorage.getItem('language');
  return savedLanguage || 'en';
};

// Deep merge translation objects
const mergeTranslations = (...translations: any[]) => {
  const deepMerge = (target: any, source: any): any => {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (
          typeof source[key] === 'object' &&
          source[key] !== null &&
          !Array.isArray(source[key]) &&
          typeof target[key] === 'object' &&
          target[key] !== null &&
          !Array.isArray(target[key])
        ) {
          result[key] = deepMerge(target[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  };

  return translations.reduce((acc, translation) => {
    return deepMerge(acc, translation);
  }, {});
};

// Resources object with modular translations
const resources = {
  en: {
    translation: mergeTranslations(
      enCommon,
      enAuth,
      enNavigation,
      enAI,
      enAdmin,
      enMentorship,
      enTemplates,
      enHome,
      enHero,
      enFeatures,
      enAnalytics,
      enDashboard,
      enIncubator,
      enPosts,
      enEvents,
      enResources,
      enProfile,
      enAbout,
      enBusinessPlan,
      enCrud,
      enFunding,
      enRoles,
      enBusinessPlans,
      enInvestments,
      enMentees,
      enModeration,
      enPortfolio,
      enReports,
      enSuperAdmin,
      enCommunity,
      enFooter,
      enContact,
      enForum
    )
  },
  ar: {
    translation: mergeTranslations(
      arCommon,
      arAuth,
      arNavigation,
      arAI,
      arAdmin,
      arMentorship,
      arTemplates,
      arHome,
      arHero,
      arFeatures,
      arAnalytics,
      arDashboard,
      arIncubator,
      arPosts,
      arEvents,
      arResources,
      arProfile,
      arAbout,
      arBusinessPlan,
      arCrud,
      arFunding,
      arRoles,
      arBusinessPlans,
      arInvestments,
      arMentees,
      arModeration,
      arPortfolio,
      arReports,
      arSuperAdmin,
      arCommunity,
      arFooter,
      arContact,
      arForum
    )
  }
};

// Initialize i18n
const initializeI18n = () => {
  const initialLanguage = getInitialLanguage();

  i18n
    // Pass the i18n instance to react-i18next
    .use(initReactI18next)
    // Detect user language
    .use(LanguageDetector)
    // Load translations from server (if needed)
    .use(Backend)
    // Initialize i18next
    .init({
      resources,
      lng: initialLanguage, // Set the initial language
      fallbackLng: 'en',
      debug: process.env.NODE_ENV === 'development',

      interpolation: {
        escapeValue: false, // React already escapes values
      },

      // RTL language list
      rtl: ['ar'],

      // Detection options
      detection: {
        order: ['localStorage', 'navigator'],
        caches: ['localStorage'],
      },

      // Ensure resources are loaded before initialization
      initImmediate: false,

      // Add this to fix the hasLanguageSomeTranslations error
      compatibilityJSON: 'v3',

      // Namespace configuration for modular translations
      defaultNS: 'translation',
      ns: ['translation'],

      // Key separator for nested translations
      keySeparator: '.',
      nsSeparator: ':',

      // React specific options
      react: {
        useSuspense: false,
        bindI18n: 'languageChanged',
        bindI18nStore: '',
        transEmptyNodeValue: '',
        transSupportBasicHtmlNodes: true,
        transKeepBasicHtmlNodesFor: ['br', 'strong', 'i'],
      },
    });

  return i18n;
};

// Export the initialized i18n instance
export default initializeI18n();

// Export utility functions
export const changeLanguage = async (language: string) => {
  try {
    await i18n.changeLanguage(language);
    localStorage.setItem('language', language);
    
    // Update document direction for RTL languages
    const isRTL = ['ar'].includes(language);
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    console.log(`Language changed to ${language}`);
  } catch (error) {
    console.error('Failed to change language:', error);
    throw error;
  }
};

export const getCurrentLanguage = () => i18n.language;

export const isRTL = () => {
  const currentLang = getCurrentLanguage();
  return ['ar'].includes(currentLang);
};

export const getSupportedLanguages = () => ['en', 'ar'];

export const getLanguageDisplayName = (langCode: string) => {
  const displayNames: Record<string, string> = {
    en: 'English',
    ar: 'العربية'
  };
  return displayNames[langCode] || langCode;
};

// Translation validation utility
export const validateTranslations = () => {
  const missingKeys: string[] = [];
  const languages = getSupportedLanguages();
  
  // This is a simplified validation - in a real app you'd want more comprehensive checking
  languages.forEach(lang => {
    const translations = resources[lang as keyof typeof resources]?.translation;
    if (!translations) {
      console.warn(`Missing translations for language: ${lang}`);
    }
  });
  
  return {
    isValid: missingKeys.length === 0,
    missingKeys
  };
};

// Export resources for testing and debugging
export { resources };
