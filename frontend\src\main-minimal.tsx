import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from './store-minimal';

// Test Redux + Router integration
const MinimalApp = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧪 Frontend Test - Step 3</h1>
      <p>✅ React is working!</p>
      <p>✅ TypeScript is working!</p>
      <p>✅ Vite is working!</p>
      <p>✅ Redux store is working!</p>
      <p>✅ React Router is working!</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#d4edda', borderRadius: '5px' }}>
        <h3>Progress:</h3>
        <ul>
          <li>✅ Redux store (minimal version)</li>
          <li>✅ React Router added</li>
          <li>🔄 Next: Add i18n</li>
          <li>🔄 Next: Add full application</li>
        </ul>
      </div>
    </div>
  );
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <MinimalApp />
      </BrowserRouter>
    </Provider>
  </StrictMode>
);
