import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

// Test without Redux to isolate the issue
const MinimalApp = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧪 Frontend Test - Debugging</h1>
      <p>✅ React is working!</p>
      <p>✅ TypeScript is working!</p>
      <p>✅ Vite is working!</p>
      <p>⚠️ Redux store has import issues - debugging...</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f8d7da', borderRadius: '5px' }}>
        <h3>Issue Found:</h3>
        <ul>
          <li>❌ Redux store imports missing RTL utilities</li>
          <li>🔧 Need to fix import paths</li>
          <li>🔧 Need to create missing files</li>
        </ul>
      </div>
    </div>
  );
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      <MinimalApp />
    </Provider>
  </StrictMode>
);
