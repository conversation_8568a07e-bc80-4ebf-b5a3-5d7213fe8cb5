/**
 * AI Error Handler Service
 * Provides graceful error handling and fallback mechanisms for AI services
 */

import { centralizedAiApi } from './centralizedAiApi';

export interface AIErrorContext {
  operation: string;
  contentType?: string;
  context?: Record<string, any>;
  language?: string;
  userId?: number;
}

export interface AIFallbackResponse {
  success: boolean;
  data?: any;
  error?: string;
  isFallback: boolean;
  fallbackReason?: string;
}

/**
 * AI Error Handler Class
 */
class AIErrorHandler {
  private retryAttempts = 3;
  private retryDelay = 1000; // 1 second

  /**
   * Execute AI operation with error handling and fallbacks
   */
  async executeWithFallback<T>(
    operation: () => Promise<T>,
    fallback: () => T,
    context: AIErrorContext
  ): Promise<AIFallbackResponse> {
    let lastError: Error | null = null;

    // Try the main operation with retries
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const result = await operation();
        return {
          success: true,
          data: result,
          isFallback: false
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Log the error
        console.warn(`AI operation failed (attempt ${attempt}/${this.retryAttempts}):`, {
          operation: context.operation,
          error: lastError.message,
          context: context.context
        });

        // Wait before retry (except on last attempt)
        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    // All retries failed, return error instead of using fallback
    console.error(`AI operation failed after ${this.retryAttempts} attempts: ${context.operation}`, {
      error: lastError?.message || 'Unknown error',
      context: context.context
    });

    return {
      success: false,
      error: lastError?.message || 'AI service unavailable after multiple retries',
      isFallback: false,
      fallbackReason: 'No fallback - showing real error'
    };
  }

  /**
   * Generate business ideas with fallback
   */
  async generateBusinessIdeas(context: {
    industry: string;
    interests: string[];
    skills: string[];
    budget?: string;
    location?: string;
  }): Promise<AIFallbackResponse> {
    const operation = async () => {
      const response = await centralizedAiApi.generateIntelligentContent({
        content_type: 'business_idea_generation',
        context,
        language: 'en'
      });

      if (!response.success) {
        throw new Error(response.error || 'AI generation failed');
      }

      return response.data;
    };

    const fallback = () => {
      throw new Error('AI service unavailable - no fallback data provided');
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'generateBusinessIdeas',
      contentType: 'business_idea_generation',
      context
    });
  }

  /**
   * Generate template recommendations with fallback
   */
  async generateTemplateRecommendations(context: {
    business_idea?: string;
    industry?: string;
    description?: string;
    target_market?: string;
    stage?: string;
  }): Promise<AIFallbackResponse> {
    const operation = async () => {
      const response = await centralizedAiApi.generateIntelligentContent({
        content_type: 'template_recommendations',
        context,
        language: 'en'
      });

      if (!response.success) {
        throw new Error(response.error || 'AI generation failed');
      }

      return response.data;
    };

    const fallback = () => {
      throw new Error('AI service unavailable - no template recommendations available');
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'generateTemplateRecommendations',
      contentType: 'template_recommendations',
      context
    });
  }

  /**
   * Generate business plan content with fallback
   */
  async generateBusinessPlanContent(context: {
    section_type: string;
    business_idea?: string;
    existing_content?: string;
    industry?: string;
  }): Promise<AIFallbackResponse> {
    const operation = async () => {
      const response = await centralizedAiApi.generateIntelligentContent({
        content_type: 'business_plan_section',
        context,
        language: 'en'
      });

      if (!response.success) {
        throw new Error(response.error || 'AI generation failed');
      }

      return response.data;
    };

    const fallback = () => {
      throw new Error('AI service unavailable - no business plan content can be generated');
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'generateBusinessPlanContent',
      contentType: 'business_plan_section',
      context
    });
  }

  /**
   * Check AI service availability
   */
  async checkAvailability(): Promise<boolean> {
    try {
      const status = await centralizedAiApi.getStatus();
      return status.available || false;
    } catch (error) {
      console.warn('AI service availability check failed:', error);
      return false;
    }
  }

  /**
   * Get AI service status with fallback
   */
  async getStatusWithFallback(): Promise<AIFallbackResponse> {
    const operation = async () => {
      return await centralizedAiApi.getStatus();
    };

    const fallback = () => {
      throw new Error('AI service status unavailable');
    };

    return this.executeWithFallback(operation, fallback, {
      operation: 'getStatus'
    });
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Set retry configuration
   */
  setRetryConfig(attempts: number, delay: number) {
    this.retryAttempts = Math.max(1, attempts);
    this.retryDelay = Math.max(100, delay);
  }
}

// Export singleton instance
export const aiErrorHandler = new AIErrorHandler();

// Export class for testing
export { AIErrorHandler };
