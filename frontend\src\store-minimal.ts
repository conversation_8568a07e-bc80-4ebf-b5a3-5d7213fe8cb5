import { configureStore, createSlice } from '@reduxjs/toolkit';

// Minimal auth slice for testing
const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  },
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    logout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
    },
  },
});

// Minimal language slice for testing
const languageSlice = createSlice({
  name: 'language',
  initialState: {
    language: 'en',
    direction: 'ltr' as 'ltr' | 'rtl',
    availableLanguages: {
      en: 'English',
      ar: 'Arabic'
    },
    isLoading: false,
    error: null,
  },
  reducers: {
    setLanguage: (state, action) => {
      state.language = action.payload;
      state.direction = action.payload === 'ar' ? 'rtl' : 'ltr';
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
  },
});

// Create minimal store
export const store = configureStore({
  reducer: {
    auth: authSlice.reducer,
    language: languageSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

// Export actions
export const { setUser, setLoading, setError, logout } = authSlice.actions;
export const { setLanguage } = languageSlice.actions;

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
