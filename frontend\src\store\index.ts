import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import only essential slices first
import authReducer from './authSlice';
import languageReducer, { initializeLanguage } from './languageSlice';
import toastReducer from './toastSlice';

// TODO: Add other slices back gradually after fixing imports
// import eventsReducer from './eventsSlice';
// import adminReducer from './adminSlice';
// import incubatorReducer from './incubatorSlice';
// import forumReducer from './forumSlice';
// import aiReducer from './aiSlice';
// import businessPlansReducer from './businessPlansSlice';
// import aiContextReducer from './aiContextSlice';
// import dashboardReducer from './dashboardSlice';
// import uiReducer from './uiSlice';
// import { aiContextMiddleware } from '../middleware/aiContextMiddleware';

// Configure persistence for auth state
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['user', 'isAuthenticated'], // Only persist user and auth status
};

// Configure persistence for language state
const languagePersistConfig = {
  key: 'language',
  storage,
};

// Combine essential reducers (others commented out temporarily)
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer),
  // events: eventsReducer, // TODO: Fix imports and re-enable
  // admin: adminReducer, // TODO: Fix imports and re-enable
  language: persistReducer(languagePersistConfig, languageReducer),
  // incubator: incubatorReducer, // TODO: Fix imports and re-enable
  // forum: forumReducer, // TODO: Fix imports and re-enable
  // ai: aiReducer, // TODO: Fix imports and re-enable
  // businessPlans: businessPlansReducer, // TODO: Fix imports and re-enable
  // aiContext: aiContextReducer, // TODO: Fix imports and re-enable
  // dashboard: dashboardReducer, // TODO: Fix imports and re-enable
  toast: toastReducer,
  // ui: uiReducer, // TODO: Fix imports and re-enable
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }), // .concat(aiContextMiddleware), // TODO: Fix imports and re-enable
});

export const persistor = persistStore(store);

// Simple store - just Redux

// Initialize language after i18n is loaded
// We'll do this with a slight delay to ensure i18n is fully initialized
setTimeout(() => {
  initializeLanguage();
}, 100);

// Note: fetchLanguage() will be called after user authentication is established
// This prevents "Authentication credentials were not provided" errors

// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
