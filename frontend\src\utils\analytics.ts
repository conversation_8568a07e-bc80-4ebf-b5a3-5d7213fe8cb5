/**
 * Performance analytics utility for tracking load times and user interactions
 */

// Types for analytics events
export type PerformanceEventType = 
  | 'page_load' 
  | 'component_load' 
  | 'api_call' 
  | 'user_interaction'
  | 'resource_load'
  | 'error';

export interface PerformanceEvent {
  type: PerformanceEventType;
  name: string;
  startTime: number;
  duration?: number;
  metadata?: Record<string, any>;
}

// In-memory storage for analytics events
const events: PerformanceEvent[] = [];

// Flag to enable/disable analytics
let analyticsEnabled = true;

// Configuration
const config = {
  sampleRate: 1.0, // Percentage of sessions to track (1.0 = 100%)
  maxEvents: 1000, // Maximum number of events to store in memory
  autoFlushInterval: 60000, // Auto-flush interval in ms (1 minute)
  apiEndpoint: '/api/analytics', // API endpoint to send analytics data
};

// Initialize analytics
export const initAnalytics = (options?: Partial<typeof config>) => {
  // Apply custom options
  Object.assign(config, options);
  
  // Determine if this session should be tracked based on sample rate
  analyticsEnabled = Math.random() <= config.sampleRate;
  
  if (analyticsEnabled) {
    // Track initial page load
    trackPageLoad();
    
    // Set up auto-flush interval
    setInterval(flushEvents, config.autoFlushInterval);
    
    // Add event listeners for page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        flushEvents();
      }
    });
    
    // Add event listener for before unload to flush events
    window.addEventListener('beforeunload', flushEvents);
    
      }
};

// Track page load performance
export const trackPageLoad = () => {
  if (!analyticsEnabled) return;
  
  // Use Performance API to get navigation timing data
  const perfData = window.performance.timing;
  const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
  const domReadyTime = perfData.domContentLoadedEventEnd - perfData.navigationStart;
  const networkLatency = perfData.responseEnd - perfData.requestStart;
  
  trackEvent('page_load', window.location.pathname, {
    pageLoadTime,
    domReadyTime,
    networkLatency,
    referrer: document.referrer,
    userAgent: navigator.userAgent,
  });
  
  // Also track performance metrics using the newer Performance API
  if (window.performance && window.performance.getEntriesByType) {
    const navigationEntries = window.performance.getEntriesByType('navigation');
    if (navigationEntries && navigationEntries.length > 0) {
      const navEntry = navigationEntries[0] as PerformanceNavigationTiming;
      trackEvent('page_load', 'navigation_timing', {
        dnsTime: navEntry.domainLookupEnd - navEntry.domainLookupStart,
        tcpTime: navEntry.connectEnd - navEntry.connectStart,
        ttfb: navEntry.responseStart - navEntry.requestStart,
        responseTime: navEntry.responseEnd - navEntry.responseStart,
        domProcessingTime: navEntry.domComplete - navEntry.domInteractive,
        loadEventTime: navEntry.loadEventEnd - navEntry.loadEventStart,
      });
    }
  }
};

// Track component load time
export const trackComponentLoad = (componentName: string, duration: number, metadata?: Record<string, any>) => {
  if (!analyticsEnabled) return;
  
  trackEvent('component_load', componentName, {
    duration,
    path: window.location.pathname,
    ...metadata,
  });
};

// Track API call performance
export const trackApiCall = (endpoint: string, startTime: number, endTime: number, success: boolean, metadata?: Record<string, any>) => {
  if (!analyticsEnabled) return;
  
  trackEvent('api_call', endpoint, {
    duration: endTime - startTime,
    success,
    ...metadata,
  });
};

// Track user interaction
export const trackUserInteraction = (action: string, metadata?: Record<string, any>) => {
  if (!analyticsEnabled) return;
  
  trackEvent('user_interaction', action, metadata);
};

// Track resource load time (images, scripts, etc.)
export const trackResourceLoad = (resourceUrl: string, duration: number, metadata?: Record<string, any>) => {
  if (!analyticsEnabled) return;
  
  trackEvent('resource_load', resourceUrl, {
    duration,
    ...metadata,
  });
};

// Track errors
export const trackError = (errorName: string, errorMessage: string, metadata?: Record<string, any>) => {
  if (!analyticsEnabled) return;
  
  trackEvent('error', errorName, {
    message: errorMessage,
    ...metadata,
  });
};

// Generic event tracking function
export const trackEvent = (
  type: PerformanceEventType, 
  name: string, 
  metadata?: Record<string, any>
) => {
  if (!analyticsEnabled) return;
  
  const event: PerformanceEvent = {
    type,
    name,
    startTime: Date.now(),
    metadata,
  };
  
  events.push(event);
  
  // If we've reached the maximum number of events, flush them
  if (events.length >= config.maxEvents) {
    flushEvents();
  }
};

// Flush events to the server
export const flushEvents = async () => {
  if (!analyticsEnabled || events.length === 0) return;
  
  try {
    // Clone the events array and clear it
    const eventsToSend = [...events];
    events.length = 0;
    
    // In a real application, you would send the events to your analytics server
    // For now, we'll just log them to the console
        
    // Example of sending to a server:
    // await fetch(config.apiEndpoint, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({
    //     events: eventsToSend,
    //     sessionId: getSessionId(),
    //     timestamp: Date.now(),
    //   }),
    // });
  } catch (error) {
    if (import.meta.env.DEV) console.error("Failed to flush analytics events:", error);
  }
};

// Get or create a session ID
const getSessionId = () => {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
};

// Create a performance monitoring hook for React components
export const createPerformanceMonitor = (componentName: string) => {
  const startTime = Date.now();
  
  return {
    trackMount: (metadata?: Record<string, any>) => {
      const mountDuration = Date.now() - startTime;
      trackComponentLoad(componentName, mountDuration, {
        event: 'mount',
        ...metadata,
      });
    },
    trackUpdate: (metadata?: Record<string, any>) => {
      trackComponentLoad(componentName, 0, {
        event: 'update',
        ...metadata,
      });
    },
    trackUnmount: (metadata?: Record<string, any>) => {
      const totalLifetime = Date.now() - startTime;
      trackComponentLoad(componentName, totalLifetime, {
        event: 'unmount',
        ...metadata,
      });
    },
  };
};
