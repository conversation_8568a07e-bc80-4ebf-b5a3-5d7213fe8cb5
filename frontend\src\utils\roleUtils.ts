/**
 * Role Utilities
 * Provides consistent role checking and permission validation throughout the application
 */

import { UserRole, PermissionLevel } from '../routes/routeConfig';

export interface User {
  id: number;
  username: string;
  email: string;
  is_superuser: boolean;
  is_staff: boolean;
  is_admin?: boolean;
  profile?: {
    active_roles?: Array<{ name: string; permission_level: string }>;
    primary_role?: { name: string; permission_level: string };
    highest_permission_level?: string;
  };
}

/**
 * Extract all roles for a user based on backend user structure
 */
export function getUserRoles(user: User | null): UserRole[] {
  if (!user) return ['user'];
  
  const roles: UserRole[] = [];
  
  // Check for super admin first (highest priority)
  if (user.is_superuser) {
    roles.push('super_admin');
  }
  
  // Check for admin role
  if (user.is_admin || user.is_staff) {
    roles.push('admin');
  }
  
  // Check for staff/moderator
  if (user.is_staff && !user.is_superuser) {
    roles.push('moderator');
  }
  
  // Extract roles from profile if available
  if (user.profile?.active_roles) {
    const profileRoles = user.profile.active_roles.map(role => role.name as UserRole);
    roles.push(...profileRoles);
  }
  
  // Add primary role if available
  if (user.profile?.primary_role?.name) {
    roles.push(user.profile.primary_role.name as UserRole);
  }
  
  // Remove duplicates and ensure at least 'user' role
  const uniqueRoles = [...new Set(roles)];
  return uniqueRoles.length > 0 ? uniqueRoles : ['user'];
}

/**
 * Extract all permissions for a user
 */
export function getUserPermissions(user: User | null): PermissionLevel[] {
  if (!user) return ['read'];

  const permissions: PermissionLevel[] = ['read'];

  // Super admin gets all permissions
  if (user.is_superuser) {
    permissions.push('write', 'moderate', 'admin', 'super_admin');
  }
  // Admin gets admin permissions
  else if (user.is_admin || user.is_staff) {
    permissions.push('write', 'moderate', 'admin');
  }
  // Use highest permission level from profile if available
  else if (user.profile?.highest_permission_level) {
    const level = user.profile.highest_permission_level;
    if (level === 'admin') {
      permissions.push('write', 'moderate', 'admin');
    } else if (level === 'moderate') {
      permissions.push('write', 'moderate');
    } else if (level === 'write') {
      permissions.push('write');
    }
  }
  // Fallback: check for specific role-based permissions
  else {
    const userRoles = getUserRoles(user);
    if (userRoles.includes('mentor') || userRoles.includes('investor')) {
      permissions.push('write');
    }
  }

  return [...new Set(permissions)];
}

/**
 * Check if user has a specific role
 */
export function hasRole(user: User | null, role: UserRole): boolean {
  const userRoles = getUserRoles(user);
  return userRoles.includes(role);
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  const userRoles = getUserRoles(user);
  return roles.some(role => userRoles.includes(role));
}

/**
 * Check if user has a specific permission
 */
export function hasPermission(user: User | null, permission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  return userPermissions.includes(permission);
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(user: User | null, permissions: PermissionLevel[]): boolean {
  const userPermissions = getUserPermissions(user);
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * Check if user is super admin
 */
export function isSuperAdmin(user: User | null): boolean {
  return user?.is_superuser === true;
}

/**
 * Check if user is admin (includes super admin)
 */
export function isAdmin(user: User | null): boolean {
  return user?.is_superuser === true || user?.is_admin === true || user?.is_staff === true;
}

/**
 * Check if user is moderator (includes admin and super admin)
 */
export function isModerator(user: User | null): boolean {
  return isAdmin(user) || hasRole(user, 'moderator');
}

/**
 * Check if user is mentor
 */
export function isMentor(user: User | null): boolean {
  return hasRole(user, 'mentor');
}

/**
 * Check if user is investor
 */
export function isInvestor(user: User | null): boolean {
  return hasRole(user, 'investor');
}

/**
 * Get the highest role for display purposes
 */
export function getHighestRole(user: User | null): UserRole {
  if (!user) return 'user';
  
  if (isSuperAdmin(user)) return 'super_admin';
  if (isAdmin(user)) return 'admin';
  if (isModerator(user)) return 'moderator';
  if (isMentor(user)) return 'mentor';
  if (isInvestor(user)) return 'investor';
  
  return 'user';
}

/**
 * Get appropriate dashboard path based on user role
 */
export function getDashboardPath(user: User | null): string {
  if (!user) return '/';
  
  if (isSuperAdmin(user)) return '/super_admin';
  if (isAdmin(user)) return '/admin';
  
  return '/dashboard';
}

/**
 * Role hierarchy for comparison (higher number = higher role)
 */
const ROLE_HIERARCHY: Record<UserRole, number> = {
  'user': 1,
  'investor': 2,
  'mentor': 3,
  'moderator': 4,
  'admin': 5,
  'super_admin': 6,
};

/**
 * Permission hierarchy for comparison (higher number = higher permission)
 */
const PERMISSION_HIERARCHY: Record<PermissionLevel, number> = {
  'read': 1,
  'write': 2,
  'moderate': 3,
  'admin': 4,
  'super_admin': 5,
};

/**
 * Compare two roles (returns true if role1 is higher than or equal to role2)
 */
export function isRoleHigherOrEqual(role1: UserRole, role2: UserRole): boolean {
  return ROLE_HIERARCHY[role1] >= ROLE_HIERARCHY[role2];
}

/**
 * Compare two permissions (returns true if perm1 is higher than or equal to perm2)
 */
export function isPermissionHigherOrEqual(perm1: PermissionLevel, perm2: PermissionLevel): boolean {
  return PERMISSION_HIERARCHY[perm1] >= PERMISSION_HIERARCHY[perm2];
}

/**
 * Check if user can access a route based on required roles and permissions
 */
export function canAccessRoute(
  user: User | null,
  requiredRoles?: UserRole[],
  requiredPermissions?: PermissionLevel[]
): boolean {
  // If no requirements, allow access
  if (!requiredRoles && !requiredPermissions) return true;
  
  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    if (!hasAnyRole(user, requiredRoles)) return false;
  }
  
  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    if (!hasAnyPermission(user, requiredPermissions)) return false;
  }
  
  return true;
}
