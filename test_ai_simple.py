import requests
import json

# Test AI status
print("🔍 Testing AI Status...")
response = requests.get('http://localhost:8000/api/ai/status/')
if response.status_code == 200:
    data = response.json()
    print(f"✅ AI Status: {data}")
else:
    print(f"❌ AI Status failed: {response.status_code}")

# Test AI chat functionality
print("\n🔍 Testing AI Chat...")
payload = {
    'message': 'Generate 3 business ideas for the technology industry',
    'language': 'en'
}

response = requests.post('http://localhost:8000/api/ai/chat/', json=payload)
if response.status_code == 200:
    data = response.json()
    print(f"✅ AI Chat Response: {data}")
    if data.get('success'):
        print(f"✅ AI Response: {data.get('response', 'No response')}")
    else:
        print(f"❌ AI Chat failed: {data.get('error')}")
else:
    print(f"❌ AI Chat failed: {response.status_code}")
    print(f"Response: {response.text}")

# Test simple AI chat endpoint
print("\n🔍 Testing Simple AI Chat...")
payload = {
    'message': 'Hello, can you help me with business ideas?'
}

response = requests.post('http://localhost:8000/api/ai/chat/', json=payload)
if response.status_code == 200:
    data = response.json()
    print(f"✅ Simple AI Chat: {data}")
else:
    print(f"❌ Simple AI Chat failed: {response.status_code}")
    print(f"Response: {response.text}")
