<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .result { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>🧪 Frontend API Integration Test</h1>
    
    <div class="test-section info">
        <h2>📊 System Status</h2>
        <button onclick="testSystemStatus()">Test System Status</button>
        <div id="system-status" class="result"></div>
    </div>

    <div class="test-section info">
        <h2>🤖 AI Service Test</h2>
        <button onclick="testAIStatus()">Test AI Status</button>
        <button onclick="testAIChat()">Test AI Chat (requires auth)</button>
        <div id="ai-status" class="result"></div>
    </div>

    <div class="test-section info">
        <h2>🔐 Authentication Test</h2>
        <button onclick="testAuthEndpoints()">Test Auth Endpoints</button>
        <div id="auth-test" class="result"></div>
    </div>

    <div class="test-section info">
        <h2>💼 Business Features Test</h2>
        <button onclick="testBusinessEndpoints()">Test Business Endpoints</button>
        <div id="business-test" class="result"></div>
    </div>

    <div class="test-section info">
        <h2>📈 Analytics Test</h2>
        <button onclick="testAnalytics()">Test Analytics</button>
        <div id="analytics-test" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return {
                    status: response.status,
                    ok: response.ok,
                    data: data
                };
            } catch (error) {
                return {
                    status: 0,
                    ok: false,
                    error: error.message
                };
            }
        }

        async function testSystemStatus() {
            const result = document.getElementById('system-status');
            result.innerHTML = '🔄 Testing system status...';
            
            const tests = [
                { name: 'Backend Health', url: `${API_BASE}/` },
                { name: 'API Root', url: `${API_BASE}/` }
            ];
            
            let html = '<h3>System Status Results:</h3>';
            
            for (const test of tests) {
                const response = await makeRequest(test.url);
                const status = response.ok ? '✅' : '❌';
                html += `<p>${status} ${test.name}: ${response.status} ${response.ok ? 'OK' : 'FAILED'}</p>`;
            }
            
            result.innerHTML = html;
        }

        async function testAIStatus() {
            const result = document.getElementById('ai-status');
            result.innerHTML = '🔄 Testing AI service...';
            
            // Test AI status endpoint
            const statusResponse = await makeRequest(`${API_BASE}/ai/status/`);
            
            let html = '<h3>AI Service Results:</h3>';
            
            if (statusResponse.ok) {
                html += `<p>✅ AI Status: ${statusResponse.status}</p>`;
                html += `<pre>${JSON.stringify(statusResponse.data, null, 2)}</pre>`;
                
                // Check if AI is available
                const aiData = statusResponse.data;
                if (aiData.status && aiData.status.available) {
                    html += '<p>✅ AI Service is AVAILABLE and ready!</p>';
                } else {
                    html += '<p>❌ AI Service is not available</p>';
                }
            } else {
                html += `<p>❌ AI Status Failed: ${statusResponse.status}</p>`;
                html += `<pre>${JSON.stringify(statusResponse, null, 2)}</pre>`;
            }
            
            result.innerHTML = html;
        }

        async function testAIChat() {
            const result = document.getElementById('ai-status');
            const currentContent = result.innerHTML;
            result.innerHTML = currentContent + '<br>🔄 Testing AI chat...';
            
            const chatResponse = await makeRequest(`${API_BASE}/ai/chat/`, {
                method: 'POST',
                body: JSON.stringify({
                    message: 'Hello, this is a test message',
                    language: 'en'
                })
            });
            
            let html = '<h4>AI Chat Test:</h4>';
            if (chatResponse.status === 401) {
                html += '<p>✅ Authentication Required (Expected - Security Working)</p>';
            } else if (chatResponse.ok) {
                html += '<p>✅ AI Chat Working</p>';
                html += `<pre>${JSON.stringify(chatResponse.data, null, 2)}</pre>`;
            } else {
                html += `<p>❌ AI Chat Failed: ${chatResponse.status}</p>`;
            }
            
            result.innerHTML = currentContent + html;
        }

        async function testAuthEndpoints() {
            const result = document.getElementById('auth-test');
            result.innerHTML = '🔄 Testing authentication endpoints...';
            
            const tests = [
                { name: 'Login Endpoint', url: `${API_BASE}/auth/login/`, method: 'POST' },
                { name: 'Register Endpoint', url: `${API_BASE}/auth/register/`, method: 'POST' }
            ];
            
            let html = '<h3>Authentication Results:</h3>';
            
            for (const test of tests) {
                const response = await makeRequest(test.url, { method: test.method });
                const status = response.status === 400 || response.status === 401 ? '✅' : (response.ok ? '✅' : '❌');
                html += `<p>${status} ${test.name}: ${response.status} (${response.status === 400 ? 'Validation Required' : response.status === 401 ? 'Auth Required' : 'Response'})</p>`;
            }
            
            result.innerHTML = html;
        }

        async function testBusinessEndpoints() {
            const result = document.getElementById('business-test');
            result.innerHTML = '🔄 Testing business endpoints...';
            
            const tests = [
                { name: 'Business Ideas', url: `${API_BASE}/incubator/business-ideas/` },
                { name: 'Business Plans', url: `${API_BASE}/incubator/business-plans/` },
                { name: 'Templates', url: `${API_BASE}/incubator/templates/` }
            ];
            
            let html = '<h3>Business Endpoints Results:</h3>';
            
            for (const test of tests) {
                const response = await makeRequest(test.url);
                const status = response.ok || response.status === 401 ? '✅' : '❌';
                html += `<p>${status} ${test.name}: ${response.status} ${response.status === 401 ? '(Auth Required)' : response.ok ? '(OK)' : '(Failed)'}</p>`;
            }
            
            result.innerHTML = html;
        }

        async function testAnalytics() {
            const result = document.getElementById('analytics-test');
            result.innerHTML = '🔄 Testing analytics endpoints...';
            
            const tests = [
                { name: 'Analytics Overview', url: `${API_BASE}/analytics/overview/` },
                { name: 'Admin Stats', url: `${API_BASE}/admin/stats/` }
            ];
            
            let html = '<h3>Analytics Results:</h3>';
            
            for (const test of tests) {
                const response = await makeRequest(test.url);
                const status = response.ok || response.status === 401 || response.status === 403 ? '✅' : '❌';
                html += `<p>${status} ${test.name}: ${response.status} ${response.status === 401 ? '(Auth Required)' : response.status === 403 ? '(Admin Required)' : response.ok ? '(OK)' : '(Failed)'}</p>`;
            }
            
            result.innerHTML = html;
        }

        // Auto-run system status test on page load
        window.onload = function() {
            testSystemStatus();
            testAIStatus();
        };
    </script>
</body>
</html>
