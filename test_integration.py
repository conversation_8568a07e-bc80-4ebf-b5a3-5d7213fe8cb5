#!/usr/bin/env python
"""
Quick integration test to verify frontend and AI are working
"""
import requests
import json
import time

def test_backend_health():
    """Test if backend is running"""
    try:
        response = requests.get('http://localhost:8000/api/ai/status/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend is running")
            print(f"   AI Service Available: {data.get('available', False)}")
            print(f"   AI Service: {data.get('service', 'unknown')}")
            return True
        else:
            print(f"❌ Backend returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend is not running (connection refused)")
        return False
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return False

def test_frontend_health():
    """Test if frontend is running"""
    try:
        response = requests.get('http://localhost:3000', timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running")
            return True
        else:
            print(f"❌ Frontend returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Frontend is not running (connection refused)")
        return False
    except Exception as e:
        print(f"❌ Frontend test failed: {e}")
        return False

def test_ai_generation():
    """Test AI content generation"""
    try:
        # Test AI idea generation
        payload = {
            'industry': 'Technology',
            'interests': 'AI, Software Development',
            'budget': 'medium'
        }
        
        response = requests.post(
            'http://localhost:8000/api/ai/generate-ideas/',
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ AI idea generation working")
                ideas = json.loads(data.get('ideas', '[]'))
                print(f"   Generated {len(ideas)} business ideas")
                return True
            else:
                print(f"❌ AI generation failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ AI generation returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI generation test failed: {e}")
        return False

def main():
    print("🧪 Integration Test - Frontend & AI")
    print("=" * 40)
    
    # Test backend
    backend_ok = test_backend_health()
    time.sleep(1)
    
    # Test frontend
    frontend_ok = test_frontend_health()
    time.sleep(1)
    
    # Test AI if backend is working
    ai_ok = False
    if backend_ok:
        ai_ok = test_ai_generation()
    
    print("\n📊 Test Results:")
    print(f"Backend:  {'✅ Working' if backend_ok else '❌ Failed'}")
    print(f"Frontend: {'✅ Working' if frontend_ok else '❌ Failed'}")
    print(f"AI:       {'✅ Working' if ai_ok else '❌ Failed'}")
    
    if backend_ok and frontend_ok and ai_ok:
        print("\n🎉 All systems working! You can now test the application.")
        print("   Frontend: http://localhost:3000")
        print("   Backend:  http://localhost:8000")
    else:
        print("\n⚠️ Some systems need attention:")
        if not backend_ok:
            print("   - Start backend: cd backend && python manage.py runserver 8000")
        if not frontend_ok:
            print("   - Start frontend: cd frontend && npm run dev")
        if not ai_ok and backend_ok:
            print("   - Check AI service configuration")

if __name__ == "__main__":
    main()
